---
layout: base.njk
title: Tech Insights & Updates
description: Stay informed with the latest technology trends, best practices, and insights to help your business thrive in the digital age.
permalink: /blog/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Tech Insights & Updates
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Stay informed with the latest technology trends, best practices, and insights 
          to help your business thrive in the digital age.
        </p>
      </div>
    </div>
  </section>

  <!-- Search and Filter -->
  <section class="py-12 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0 animate-slide-up">
        <!-- Search -->
        <div class="relative w-full lg:w-96">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input
            type="text"
            id="search-input"
            placeholder="Search articles..."
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <!-- Categories -->
        <div class="flex flex-wrap gap-3">
          <button class="category-btn active px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 bg-primary-600 text-white" data-category="all">
            All Posts
          </button>
          <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-category="cloud">
            Cloud Computing
          </button>
          <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-category="security">
            Cybersecurity
          </button>
          <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-category="digital-transformation">
            Digital Transformation
          </button>
          <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-category="best-practices">
            Best Practices
          </button>
          <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-category="trends">
            Industry Trends
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Featured Post -->
  {% if collections.blog.length > 0 %}
    {% set featuredPost = collections.blog[0] %}
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-2xl overflow-hidden shadow-xl animate-scale-in">
          <div class="grid grid-cols-1 lg:grid-cols-2">
            <div class="relative h-64 lg:h-auto">
              <img
                src="{{ featuredPost.data.image or 'https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop' }}"
                alt="{{ featuredPost.data.title }}"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-4 left-4">
                <span class="bg-accent-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Featured
                </span>
              </div>
            </div>
            
            <div class="p-8 lg:p-12 flex flex-col justify-center">
              <div class="flex flex-wrap gap-2 mb-4">
                {% if featuredPost.data.tags %}
                  {% for tag in featuredPost.data.tags %}
                    <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                      </svg>
                      {{ tag }}
                    </span>
                  {% endfor %}
                {% endif %}
              </div>
              
              <h2 class="text-3xl font-bold text-gray-900 mb-4">
                {{ featuredPost.data.title }}
              </h2>
              
              <p class="text-gray-600 mb-6 leading-relaxed">
                {{ featuredPost.data.excerpt or featuredPost.content | excerpt }}
              </p>
              
              <div class="flex items-center justify-between text-sm text-gray-500 mb-6">
                <div class="flex items-center space-x-4">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    {{ featuredPost.data.author or 'NexTech Team' }}
                  </div>
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    {{ featuredPost.date | readableDate }}
                  </div>
                </div>
                <span>{{ featuredPost.data.readTime or '5 min read' }}</span>
              </div>
              
              <a
                href="{{ featuredPost.url }}"
                class="inline-flex items-center text-primary-600 font-semibold hover:text-primary-700 group"
              >
                Read Full Article
                <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  {% endif %}

  <!-- Blog Posts Grid -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="blog-grid">
        {% for post in collections.blog %}
          {% if not loop.first %}
            <article
              class="blog-post bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group animate-scale-in"
              style="animation-delay: {{ loop.index0 * 0.1 }}s;"
              data-category="{{ post.data.category or 'general' }}"
              data-title="{{ post.data.title | lower }}"
              data-excerpt="{{ post.data.excerpt or post.content | excerpt | lower }}"
            >
              <div class="relative h-48 overflow-hidden">
                <img
                  src="{{ post.data.image or 'https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop' }}"
                  alt="{{ post.data.title }}"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              
              <div class="p-6">
                <div class="flex flex-wrap gap-2 mb-3">
                  {% if post.data.tags %}
                    {% for tag in post.data.tags.slice(0, 2) %}
                      <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ tag }}
                      </span>
                    {% endfor %}
                  {% endif %}
                </div>
                
                <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
                  {{ post.data.title }}
                </h3>
                
                <p class="text-gray-600 mb-4 leading-relaxed">
                  {{ post.data.excerpt or post.content | excerpt }}
                </p>
                
                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div class="flex items-center space-x-3">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      {{ post.data.author or 'NexTech Team' }}
                    </div>
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      {{ post.date | readableDate }}
                    </div>
                  </div>
                  <span>{{ post.data.readTime or '5 min read' }}</span>
                </div>
                
                <a
                  href="{{ post.url }}"
                  class="inline-flex items-center text-primary-600 font-semibold hover:text-primary-700 group/link"
                >
                  Read More
                  <svg class="ml-2 w-4 h-4 group-hover/link:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                  </svg>
                </a>
              </div>
            </article>
          {% endif %}
        {% endfor %}
      </div>
      
      <!-- No Results Message -->
      <div id="no-results" class="text-center py-12 hidden">
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">
          No articles found
        </h3>
        <p class="text-gray-600 mb-8">
          Try adjusting your search terms or selected category.
        </p>
        <button
          id="clear-filters"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Clear Filters
        </button>
      </div>
    </div>
  </section>

  <!-- Newsletter Signup -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Stay Updated with Tech Insights
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Subscribe to our newsletter and never miss the latest technology trends,
          best practices, and expert insights for your business.
        </p>

        <form class="max-w-md mx-auto" action="#" method="POST">
          <div class="flex rounded-lg overflow-hidden shadow-lg">
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              required
              class="flex-1 px-6 py-4 text-gray-900 placeholder-gray-500 focus:outline-none"
            />
            <button
              type="submit"
              class="bg-accent-600 text-white px-8 py-4 font-semibold hover:bg-accent-700 transition-colors"
            >
              Subscribe
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  const categoryButtons = document.querySelectorAll('.category-btn');
  const blogPosts = document.querySelectorAll('.blog-post');
  const noResults = document.getElementById('no-results');
  const clearFilters = document.getElementById('clear-filters');

  let currentCategory = 'all';
  let currentSearch = '';

  function filterPosts() {
    let visibleCount = 0;

    blogPosts.forEach(post => {
      const category = post.getAttribute('data-category');
      const title = post.getAttribute('data-title');
      const excerpt = post.getAttribute('data-excerpt');

      const matchesCategory = currentCategory === 'all' || category === currentCategory;
      const matchesSearch = currentSearch === '' ||
                           title.includes(currentSearch) ||
                           excerpt.includes(currentSearch);

      if (matchesCategory && matchesSearch) {
        post.style.display = 'block';
        post.classList.add('animate-scale-in');
        visibleCount++;
      } else {
        post.style.display = 'none';
      }
    });

    if (visibleCount === 0) {
      noResults.classList.remove('hidden');
    } else {
      noResults.classList.add('hidden');
    }
  }

  // Search functionality
  searchInput.addEventListener('input', function() {
    currentSearch = this.value.toLowerCase();
    filterPosts();
  });

  // Category filtering
  categoryButtons.forEach(button => {
    button.addEventListener('click', function() {
      currentCategory = this.getAttribute('data-category');

      // Update active button
      categoryButtons.forEach(btn => {
        btn.classList.remove('active', 'bg-primary-600', 'text-white');
        btn.classList.add('bg-gray-100', 'text-gray-700');
      });

      this.classList.add('active', 'bg-primary-600', 'text-white');
      this.classList.remove('bg-gray-100', 'text-gray-700');

      filterPosts();
    });
  });

  // Clear filters
  clearFilters.addEventListener('click', function() {
    currentSearch = '';
    currentCategory = 'all';
    searchInput.value = '';

    // Reset category buttons
    categoryButtons.forEach(btn => {
      btn.classList.remove('active', 'bg-primary-600', 'text-white');
      btn.classList.add('bg-gray-100', 'text-gray-700');
    });

    categoryButtons[0].classList.add('active', 'bg-primary-600', 'text-white');
    categoryButtons[0].classList.remove('bg-gray-100', 'text-gray-700');

    filterPosts();
  });
});
</script>
