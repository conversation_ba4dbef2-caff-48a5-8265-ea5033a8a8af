@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prose styling for blog posts */
.prose {
  @apply text-gray-700 leading-relaxed;
}

.prose h1 {
  @apply text-4xl font-bold text-gray-900 mb-6 mt-8 first:mt-0;
}

.prose h2 {
  @apply text-3xl font-bold text-gray-900 mb-4 mt-8;
}

.prose h3 {
  @apply text-2xl font-semibold text-gray-900 mb-3 mt-6;
}

.prose h4 {
  @apply text-xl font-semibold text-gray-900 mb-2 mt-4;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul,
.prose ol {
  @apply mb-4 pl-6;
}

.prose li {
  @apply mb-2;
}

.prose ul li {
  @apply list-disc;
}

.prose ol li {
  @apply list-decimal;
}

.prose a {
  @apply text-primary-600 hover:text-primary-700 underline;
}

.prose strong {
  @apply font-semibold text-gray-900;
}

.prose em {
  @apply italic;
}

.prose code {
  @apply bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono;
}

.prose pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto mb-4;
}

.prose pre code {
  @apply bg-transparent text-gray-100 p-0;
}

.prose blockquote {
  @apply border-l-4 border-primary-500 pl-4 italic text-gray-600 my-4;
}

.prose img {
  @apply rounded-lg shadow-lg my-6;
}

.prose table {
  @apply w-full border-collapse border border-gray-300 my-4;
}

.prose th,
.prose td {
  @apply border border-gray-300 px-4 py-2;
}

.prose th {
  @apply bg-gray-100 font-semibold;
}
