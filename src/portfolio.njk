---
layout: base.njk
title: Our Portfolio
description: Discover how we've helped businesses transform their operations and achieve remarkable results through innovative IT solutions.
permalink: /portfolio/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Our Portfolio
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Discover how we've helped businesses transform their operations and 
          achieve remarkable results through innovative IT solutions.
        </p>
      </div>
    </div>
  </section>

  <!-- Filter Section -->
  <section class="py-12 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-wrap justify-center gap-4 animate-slide-up">
        <button class="filter-btn active px-6 py-3 rounded-full font-medium transition-all duration-300 bg-primary-600 text-white shadow-lg" data-filter="all">
          All Projects
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="cloud">
          Cloud Solutions
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="security">
          Cybersecurity
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="transformation">
          Digital Transformation
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="development">
          Custom Development
        </button>
      </div>
    </div>
  </section>

  <!-- Projects Grid -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="portfolio-grid">
        {% set projects = [
          {
            id: 1,
            title: "E-commerce Cloud Migration",
            category: "cloud",
            client: "RetailMax Inc.",
            description: "Complete cloud migration for a growing e-commerce platform, reducing costs by 35% and improving performance.",
            image: "https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            results: ["35% cost reduction", "99.9% uptime achieved", "50% faster load times", "3x scalability increase"],
            date: "2024",
            tags: ["AWS", "Migration", "E-commerce"]
          },
          {
            id: 2,
            title: "Financial Services Security Overhaul",
            category: "security",
            client: "SecureBank Ltd.",
            description: "Comprehensive cybersecurity implementation for a financial institution, ensuring compliance and protection.",
            image: "https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            results: ["Zero security incidents", "SOC 2 compliance achieved", "24/7 monitoring implemented", "Staff training completed"],
            date: "2024",
            tags: ["Security", "Compliance", "Finance"]
          },
          {
            id: 3,
            title: "Manufacturing Digital Transformation",
            category: "transformation",
            client: "TechManufacturing Co.",
            description: "Complete digital transformation of manufacturing processes, implementing IoT and automation solutions.",
            image: "https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            results: ["40% efficiency increase", "Real-time monitoring", "Predictive maintenance", "Quality improvements"],
            date: "2024",
            tags: ["IoT", "Automation", "Manufacturing"]
          },
          {
            id: 4,
            title: "Healthcare Management System",
            category: "development",
            client: "HealthCare Plus",
            description: "Custom patient management system with integrated billing and appointment scheduling.",
            image: "https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            results: ["90% faster patient processing", "Integrated billing system", "Mobile accessibility", "HIPAA compliance"],
            date: "2023",
            tags: ["Healthcare", "Custom Development", "HIPAA"]
          },
          {
            id: 5,
            title: "Multi-Cloud Infrastructure",
            category: "cloud",
            client: "GlobalTech Solutions",
            description: "Multi-cloud infrastructure setup with disaster recovery and automated backup systems.",
            image: "https://images.pexels.com/photos/159888/pexels-photo-159888.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            results: ["Multi-cloud redundancy", "Automated backups", "Disaster recovery", "Cost optimization"],
            date: "2023",
            tags: ["Multi-cloud", "Disaster Recovery", "Backup"]
          },
          {
            id: 6,
            title: "EdTech Platform Security",
            category: "security",
            client: "EduPlatform Inc.",
            description: "Security implementation for online education platform protecting student data and privacy.",
            image: "https://images.pexels.com/photos/3184298/pexels-photo-3184298.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            results: ["Student data protection", "FERPA compliance", "Secure authentication", "Privacy controls"],
            date: "2023",
            tags: ["EdTech", "Privacy", "FERPA"]
          }
        ] %}
        
        {% for project in projects %}
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: {{ loop.index0 * 0.1 }}s;"
            data-category="{{ project.category }}"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="{{ project.image }}"
                alt="{{ project.title }}"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>{{ project.date }}</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                {% for tag in project.tags %}
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    {{ tag }}
                  </span>
                {% endfor %}
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                {{ project.title }}
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: {{ project.client }}
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                {{ project.description }}
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  {% for result in project.results.slice(0, 3) %}
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      {{ result }}
                    </li>
                  {% endfor %}
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-4">
          Project Success Metrics
        </h2>
        <p class="text-xl text-blue-100">
          Real results that matter to your business
        </p>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
        {% set stats = [
          { value: "200+", label: "Projects Completed" },
          { value: "98%", label: "Client Satisfaction" },
          { value: "35%", label: "Average Cost Savings" },
          { value: "24/7", label: "Support Coverage" }
        ] %}

        {% for stat in stats %}
          <div
            class="text-center animate-scale-in"
            style="animation-delay: {{ loop.index0 * 0.1 }}s;"
          >
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">
              {{ stat.value }}
            </div>
            <div class="text-blue-100 font-medium">{{ stat.label }}</div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-6">
          Ready to Start Your Success Story?
        </h2>
        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
          Join our portfolio of successful clients. Let's discuss how we can
          help transform your business with innovative IT solutions.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-primary-700 transition-all duration-300 group"
        >
          Start Your Project
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const filterButtons = document.querySelectorAll('.filter-btn');
  const portfolioItems = document.querySelectorAll('.portfolio-item');

  filterButtons.forEach(button => {
    button.addEventListener('click', function() {
      const filter = this.getAttribute('data-filter');

      // Update active button
      filterButtons.forEach(btn => {
        btn.classList.remove('active', 'bg-primary-600', 'text-white', 'shadow-lg');
        btn.classList.add('bg-gray-100', 'text-gray-700');
      });

      this.classList.add('active', 'bg-primary-600', 'text-white', 'shadow-lg');
      this.classList.remove('bg-gray-100', 'text-gray-700');

      // Filter items
      portfolioItems.forEach(item => {
        const category = item.getAttribute('data-category');
        if (filter === 'all' || category === filter) {
          item.style.display = 'block';
          item.classList.add('animate-scale-in');
        } else {
          item.style.display = 'none';
        }
      });
    });
  });
});
</script>
