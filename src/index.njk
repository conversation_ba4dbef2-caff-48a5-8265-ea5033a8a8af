---
layout: base.njk
title: Home
description: Transform your business with smart IT solutions. Empowering SMEs with cutting-edge technology services.
permalink: /
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="animate-slide-up">
          <h1 class="text-4xl md:text-6xl font-bold leading-tight mb-6">
            Transform Your Business with 
            <span class="text-accent-400"> Smart IT Solutions</span>
          </h1>
          <p class="text-xl text-blue-100 mb-8 leading-relaxed">
            Empower your SME with cutting-edge technology. We deliver reliable, 
            scalable IT services that drive growth and efficiency.
          </p>
          <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <a
              href="/contact/"
              class="bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-accent-700 transition-all duration-300 flex items-center justify-center group"
            >
              Get Free Consultation
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a
              href="/services/"
              class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-all duration-300 text-center"
            >
              Explore Services
            </a>
          </div>
        </div>
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <img
            src="https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
            alt="IT Solutions"
            class="rounded-2xl shadow-2xl"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Performance Metrics -->
  <section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
        {% for metric in metrics %}
          <div
            class="text-center animate-scale-in"
            style="animation-delay: {{ loop.index0 * 0.1 }}s;"
          >
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
              {{ metric.value }}
            </div>
            <div class="text-gray-600 font-medium">{{ metric.label }}</div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Services Grid -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Comprehensive IT Services
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          From cloud migration to cybersecurity, we provide end-to-end IT solutions 
          tailored for small and medium enterprises.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% for service in services %}
          <a
            href="/services/{{ service.id }}/"
            class="group bg-white rounded-xl p-8 border border-gray-200 hover:border-primary-300 hover:shadow-xl transition-all duration-300 animate-scale-in"
            style="animation-delay: {{ loop.index0 * 0.1 }}s;"
          >
            <div class="text-primary-600 mb-4 group-hover:scale-110 transition-transform duration-300">
              {% if service.icon == 'cloud' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
              {% elif service.icon == 'shield' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              {% elif service.icon == 'zap' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              {% elif service.icon == 'users' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              {% elif service.icon == 'check-circle' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              {% elif service.icon == 'star' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              {% endif %}
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
              {{ service.title }}
            </h3>
            <p class="text-gray-600 mb-4 leading-relaxed">
              {{ service.description }}
            </p>
            <div class="flex items-center text-primary-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
              Learn more 
              <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </div>
          </a>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Testimonials -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          What Our Clients Say
        </h2>
        <p class="text-xl text-gray-600">
          Don't just take our word for it. Here's what our clients have to say.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {% for testimonial in testimonials %}
          <div
            class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 animate-scale-in"
            style="animation-delay: {{ loop.index0 * 0.2 }}s;"
          >
            <div class="flex items-center mb-6">
              <img
                src="{{ testimonial.image }}"
                alt="{{ testimonial.author }}"
                class="w-12 h-12 rounded-full mr-4"
              />
              <div>
                <h4 class="font-semibold text-gray-900">{{ testimonial.author }}</h4>
                <p class="text-gray-600 text-sm">{{ testimonial.position }}</p>
              </div>
            </div>
            <p class="text-gray-700 italic leading-relaxed">
              "{{ testimonial.quote }}"
            </p>
            <div class="flex text-yellow-400 mt-4">
              {% for i in range(0, 5) %}
                <svg class="w-5 h-5 fill-current" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              {% endfor %}
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Transform Your Business?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Join hundreds of SMEs who have accelerated their growth with our IT solutions.
          Get a free consultation and discover how we can help your business thrive.
        </p>

        <form class="max-w-md mx-auto mb-8" action="#" method="POST">
          <div class="flex rounded-lg overflow-hidden shadow-lg">
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              required
              class="flex-1 px-6 py-4 text-gray-900 placeholder-gray-500 focus:outline-none"
            />
            <button
              type="submit"
              class="bg-accent-600 text-white px-8 py-4 font-semibold hover:bg-accent-700 transition-colors"
            >
              Subscribe
            </button>
          </div>
        </form>

        <a
          href="/contact/"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Start Your Digital Journey
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>
