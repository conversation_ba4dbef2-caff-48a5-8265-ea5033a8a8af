---
layout: base.njk
title: About Us
description: Learn about NexTech Solutions - your trusted partner for innovative IT solutions. Discover our mission, values, and the expert team behind our success.
permalink: /about/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          About NexTech Solutions
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          We're passionate about empowering small and medium enterprises with 
          cutting-edge technology solutions that drive growth and innovation.
        </p>
      </div>
    </div>
  </section>

  <!-- Mission & Vision -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        <div class="animate-slide-up">
          <h2 class="text-4xl font-bold text-gray-900 mb-6">
            Our Mission
          </h2>
          <p class="text-xl text-gray-600 mb-8 leading-relaxed">
            To democratize enterprise-level technology solutions for small and medium businesses, 
            enabling them to compete effectively in the digital marketplace while maintaining 
            their unique identity and values.
          </p>
          <p class="text-gray-600 leading-relaxed">
            We believe that every business, regardless of size, deserves access to innovative 
            technology solutions that can transform their operations, enhance security, and 
            drive sustainable growth. Our mission is to bridge the technology gap and make 
            advanced IT solutions accessible and affordable for SMEs.
          </p>
        </div>
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <img
            src="https://images.pexels.com/photos/3184298/pexels-photo-3184298.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
            alt="Our Mission"
            class="rounded-2xl shadow-2xl"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Values -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Core Values
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          These principles guide everything we do and shape how we serve our clients
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% set values = [
          {
            icon: "shield",
            title: "Trust & Security",
            description: "We prioritize the security and confidentiality of your data, implementing robust measures to protect your business assets."
          },
          {
            icon: "users",
            title: "Client Partnership",
            description: "We view our clients as partners, working collaboratively to understand and address their unique challenges and goals."
          },
          {
            icon: "zap",
            title: "Innovation",
            description: "We stay at the forefront of technology trends, continuously exploring new solutions to benefit our clients."
          },
          {
            icon: "check-circle",
            title: "Reliability",
            description: "Our clients depend on us for consistent, high-quality service delivery and proactive support."
          },
          {
            icon: "star",
            title: "Excellence",
            description: "We strive for excellence in every project, ensuring solutions that exceed expectations and deliver real value."
          },
          {
            icon: "heart",
            title: "Integrity",
            description: "We conduct business with honesty, transparency, and ethical practices in all our interactions."
          }
        ] %}
        
        {% for value in values %}
          <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center text-primary-600 mb-6">
              {% if value.icon == 'shield' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              {% elif value.icon == 'users' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              {% elif value.icon == 'zap' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              {% elif value.icon == 'check-circle' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              {% elif value.icon == 'star' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              {% elif value.icon == 'heart' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              {% endif %}
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ value.title }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ value.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Team Section -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Meet Our Expert Team
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Our diverse team of technology experts brings decades of combined experience 
          in delivering innovative IT solutions.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% set team = [
          {
            name: "Sarah Johnson",
            role: "CEO & Founder",
            image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
            bio: "15+ years in enterprise technology with expertise in digital transformation and strategic IT planning."
          },
          {
            name: "Michael Chen",
            role: "CTO",
            image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
            bio: "Cloud architecture specialist with extensive experience in AWS, Azure, and cybersecurity implementations."
          },
          {
            name: "Emma Rodriguez",
            role: "Head of Operations",
            image: "https://images.pexels.com/photos/3586798/pexels-photo-3586798.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
            bio: "Operations expert focused on service delivery excellence and client relationship management."
          },
          {
            name: "David Thompson",
            role: "Security Specialist",
            image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
            bio: "Cybersecurity expert with certifications in ethical hacking and compliance frameworks."
          },
          {
            name: "Lisa Park",
            role: "Cloud Solutions Architect",
            image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
            bio: "Multi-cloud specialist helping businesses optimize their cloud infrastructure and reduce costs."
          },
          {
            name: "James Wilson",
            role: "DevOps Engineer",
            image: "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
            bio: "Automation expert specializing in CI/CD pipelines and infrastructure as code implementations."
          }
        ] %}
        
        {% for member in team %}
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="relative h-64 overflow-hidden">
              <img
                src="{{ member.image }}"
                alt="{{ member.name }}"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-2">{{ member.name }}</h3>
              <p class="text-primary-600 font-semibold mb-4">{{ member.role }}</p>
              <p class="text-gray-600 leading-relaxed">{{ member.bio }}</p>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Company Timeline -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Journey
        </h2>
        <p class="text-xl text-gray-600">
          From startup to trusted IT partner - here's how we've grown
        </p>
      </div>

      <div class="relative">
        <!-- Timeline line -->
        <div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary-200"></div>

        {% set timeline = [
          {
            year: "2010",
            title: "Company Founded",
            description: "NexTech Solutions was established with a vision to democratize enterprise IT for SMEs."
          },
          {
            year: "2013",
            title: "First Major Client",
            description: "Successfully completed our first major cloud migration project, establishing our reputation in the market."
          },
          {
            year: "2016",
            title: "Team Expansion",
            description: "Grew our team to 15 specialists and expanded our service offerings to include cybersecurity."
          },
          {
            year: "2019",
            title: "100+ Clients",
            description: "Reached the milestone of serving over 100 satisfied clients across various industries."
          },
          {
            year: "2022",
            title: "Digital Transformation Focus",
            description: "Launched comprehensive digital transformation services to help businesses adapt to the digital age."
          },
          {
            year: "2025",
            title: "Continued Innovation",
            description: "Today, we continue to innovate and expand our services to meet evolving technology needs."
          }
        ] %}

        {% for event in timeline %}
          <div class="relative flex items-center mb-12 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.2 }}s;">
            {% if loop.index0 % 2 == 0 %}
              <!-- Left side -->
              <div class="w-1/2 pr-8 text-right">
                <div class="bg-white rounded-lg p-6 shadow-lg">
                  <div class="text-2xl font-bold text-primary-600 mb-2">{{ event.year }}</div>
                  <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ event.title }}</h3>
                  <p class="text-gray-600">{{ event.description }}</p>
                </div>
              </div>
              <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg"></div>
              <div class="w-1/2 pl-8"></div>
            {% else %}
              <!-- Right side -->
              <div class="w-1/2 pr-8"></div>
              <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg"></div>
              <div class="w-1/2 pl-8">
                <div class="bg-white rounded-lg p-6 shadow-lg">
                  <div class="text-2xl font-bold text-primary-600 mb-2">{{ event.year }}</div>
                  <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ event.title }}</h3>
                  <p class="text-gray-600">{{ event.description }}</p>
                </div>
              </div>
            {% endif %}
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-4">
          Our Impact in Numbers
        </h2>
        <p class="text-xl text-blue-100">
          Real results that demonstrate our commitment to client success
        </p>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
        {% set stats = [
          { value: "500+", label: "Clients Served" },
          { value: "15+", label: "Years Experience" },
          { value: "99.9%", label: "Uptime Guarantee" },
          { value: "24/7", label: "Support Available" }
        ] %}

        {% for stat in stats %}
          <div class="text-center animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="text-4xl md:text-5xl font-bold text-white mb-2">{{ stat.value }}</div>
            <div class="text-blue-100 font-medium">{{ stat.label }}</div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-6">
          Ready to Partner with Us?
        </h2>
        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
          Join hundreds of businesses that trust NexTech Solutions for their IT needs.
          Let's discuss how we can help transform your technology infrastructure.
        </p>
        <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
          <a
            href="/contact/"
            class="inline-flex items-center bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-primary-700 transition-all duration-300 group"
          >
            Get Free Consultation
            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a
            href="/portfolio/"
            class="inline-flex items-center border-2 border-primary-600 text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-primary-600 hover:text-white transition-all duration-300"
          >
            View Our Work
          </a>
        </div>
      </div>
    </div>
  </section>
</div>
