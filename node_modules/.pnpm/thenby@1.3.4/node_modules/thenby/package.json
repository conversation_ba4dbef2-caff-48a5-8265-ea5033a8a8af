{"name": "thenby", "version": "1.3.4", "description": "Micro library for sorting arrays using the firstBy().thenBy().thenBy() syntax", "main": "thenBy.module.js", "types": "thenBy.module.d.ts", "repository": {"type": "git", "url": "https://github.com/Teun/thenBy.js.git"}, "keywords": ["sort", "order", "sorting", "arrays", "multiple"], "files": ["thenBy.module.js", "thenBy.module.d.ts", "thenBy.min.js"], "scripts": {"test": "gulp", "build": "gulp build"}, "author": "<PERSON><PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/Teun/thenBy.js/issues"}, "homepage": "https://github.com/Teun/thenBy.js", "devDependencies": {"chai": "^3.5.0", "gulp": "^4.0.2", "gulp-insert": "^0.5.0", "gulp-mocha": "^7.0.2", "gulp-rename": "^1.2.2", "gulp-replace": "^0.5.4", "gulp-uglify": "^2.1.2", "gulp-umd": "^2.0.0", "performance-now": "^0.2.0"}, "dependencies": {}}