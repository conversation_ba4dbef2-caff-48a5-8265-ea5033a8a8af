/*** Copyright 2020 <PERSON><PERSON> Licensed under the Apache License, Version 2.0 ***/
!function(n,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?module.exports=t():n.firstBy=t()}(this,function(){return function(){function n(n){return n}function t(n){return"string"==typeof n?n.toLowerCase():n}function e(e,r){if(r="object"==typeof r?r:{direction:r},"function"!=typeof e){var i=e;e=function(n){return n[i]?n[i]:""}}if(1===e.length){var o=e,f=r.ignoreCase?t:n,u=r.cmp||function(n,t){return n<t?-1:n>t?1:0};e=function(n,t){return u(f(o(n)),f(o(t)))}}const c={"-1":"",desc:""};return r.direction in c?function(n,t){return-e(n,t)}:e}function r(n,t){var i="function"==typeof this&&!this.firstBy&&this,o=e(n,t),f=i?function(n,t){return i(n,t)||o(n,t)}:o;return f.thenBy=r,f}return r.firstBy=r,r}()});