{"name": "postcss-reporter", "version": "7.1.0", "description": "Log PostCSS messages in the console", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">=10"}, "repository": "postcss/postcss-reporter", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://davidtheclark.com"}, "license": "MIT", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "peerDependencies": {"postcss": "^8.1.0"}, "dependencies": {"picocolors": "^1.0.0", "thenby": "^1.3.4"}}