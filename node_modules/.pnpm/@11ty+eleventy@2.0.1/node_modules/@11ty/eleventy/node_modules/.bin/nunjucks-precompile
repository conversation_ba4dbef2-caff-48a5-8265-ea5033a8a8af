#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/sanrado/node_modules/.pnpm/nunjucks@3.2.4_chokidar@3.6.0/node_modules/nunjucks/bin/node_modules:/Users/<USER>/Sites/sanrado/node_modules/.pnpm/nunjucks@3.2.4_chokidar@3.6.0/node_modules/nunjucks/node_modules:/Users/<USER>/Sites/sanrado/node_modules/.pnpm/nunjucks@3.2.4_chokidar@3.6.0/node_modules:/Users/<USER>/Sites/sanrado/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/sanrado/node_modules/.pnpm/nunjucks@3.2.4_chokidar@3.6.0/node_modules/nunjucks/bin/node_modules:/Users/<USER>/Sites/sanrado/node_modules/.pnpm/nunjucks@3.2.4_chokidar@3.6.0/node_modules/nunjucks/node_modules:/Users/<USER>/Sites/sanrado/node_modules/.pnpm/nunjucks@3.2.4_chokidar@3.6.0/node_modules:/Users/<USER>/Sites/sanrado/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../nunjucks@3.2.4_chokidar@3.6.0/node_modules/nunjucks/bin/precompile" "$@"
else
  exec node  "$basedir/../../../../../../nunjucks@3.2.4_chokidar@3.6.0/node_modules/nunjucks/bin/precompile" "$@"
fi
