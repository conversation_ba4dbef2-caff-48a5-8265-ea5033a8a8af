{"name": "@11ty/eleventy", "version": "2.0.1", "description": "A simpler (static) site generator.", "publishConfig": {"access": "public"}, "main": "src/Eleventy.js", "types": "src/index.d.ts", "bin": {"eleventy": "./cmd.js"}, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "keywords": ["static-site-generator", "static-site", "ssg", "documentation", "website", "j<PERSON><PERSON><PERSON>", "blog", "templates", "generator", "framework", "eleventy", "11ty", "html", "markdown", "liquid", "nunjucks", "pug", "handlebars", "mustache", "ejs", "haml"], "scripts": {"default": "npm run test", "format": "prettier src/ --write", "test": "npx ava --verbose", "lint-staged": "lint-staged", "coverage": "npx nyc ava && npx nyc report --reporter=json-summary && cp coverage/coverage-summary.json docs-src/_data/coverage.json && node cmd.js --config=docs-src/.eleventy.docs.js", "prepare": "husky install"}, "author": {"name": "<PERSON>", "email": "zach<PERSON><PERSON><EMAIL>", "url": "https://zachleat.com/"}, "repository": {"type": "git", "url": "git://github.com/11ty/eleventy.git"}, "bugs": "https://github.com/11ty/eleventy/issues", "homepage": "https://www.11ty.dev/", "ava": {"environmentVariables": {}, "failFast": true, "files": ["./test/*.js", "./test/_issues/**/*test.js"], "ignoredByWatcher": ["./test/stubs*/**/*", ".cache"]}, "lint-staged": {"*.{js,css,md}": ["prettier --write"]}, "devDependencies": {"@11ty/eleventy-plugin-syntaxhighlight": "^4.2.0", "@11ty/eleventy-plugin-vue": "1.0.0-canary.8", "@vue/server-renderer": "^3.2.47", "ava": "^5.2.0", "husky": "^8.0.3", "js-yaml": "^4.1.0", "lint-staged": "^13.2.0", "markdown-it-emoji": "^2.0.2", "marked": "^4.3.0", "nyc": "^15.1.0", "prettier": "^2.8.7", "pretty": "^2.0.0", "rimraf": "^4.4.1", "sass": "^1.60.0", "vue": "^3.2.47"}, "dependencies": {"@11ty/dependency-tree": "^2.0.1", "@11ty/eleventy-dev-server": "^1.0.4", "@11ty/eleventy-utils": "^1.0.1", "@11ty/lodash-custom": "^4.17.21", "@iarna/toml": "^2.2.5", "@sindresorhus/slugify": "^1.1.2", "bcp-47-normalize": "^1.1.1", "chokidar": "^3.5.3", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dependency-graph": "^0.11.0", "ejs": "^3.1.9", "fast-glob": "^3.2.12", "graceful-fs": "^4.2.11", "gray-matter": "^4.0.3", "hamljs": "^0.6.2", "handlebars": "^4.7.7", "is-glob": "^4.0.3", "iso-639-1": "^2.1.15", "kleur": "^4.1.5", "liquidjs": "^10.7.0", "luxon": "^3.3.0", "markdown-it": "^13.0.1", "micromatch": "^4.0.5", "minimist": "^1.2.8", "moo": "^0.5.2", "multimatch": "^5.0.0", "mustache": "^4.2.0", "normalize-path": "^3.0.0", "nunjucks": "^3.2.3", "path-to-regexp": "^6.2.1", "please-upgrade-node": "^3.2.0", "posthtml": "^0.16.6", "posthtml-urls": "^1.0.0", "pug": "^3.0.2", "recursive-copy": "^2.0.14", "semver": "^7.3.8", "slugify": "^1.6.6"}}