{"name": "markdown-it-anchor", "version": "8.6.7", "description": "Header anchors for markdown-it.", "keywords": ["markdown", "markdown-it", "markdown-it-plugin", "header", "anchor"], "license": "Unlicense", "contributors": ["Val (https://val.codejam.info/)", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/nagaozen/)"], "files": ["README.md", "UNLICENSE", "dist/*", "runkit.js", "types/*"], "main": "dist/markdownItAnchor.js", "module": "dist/markdownItAnchor.mjs", "types": "./types/index.d.ts", "repository": "valeriangalliat/markdown-it-anchor", "scripts": {"build": "microbundle --generateTypes false", "dev": "microbundle watch", "lint": "standard index.js test.js", "prepare": "npm run build && npm test", "test": "npm run lint && ava test.js"}, "devDependencies": {"ava": "^5.1.0", "markdown-it": "*", "markdown-it-attrs": "^3.0.1", "microbundle": "^0.15.1", "source-map-support": "^0.5.19", "standard": "^14.3.1"}, "peerDependencies": {"@types/markdown-it": "*", "markdown-it": "*"}, "source": "index.js", "unpkg": "dist/markdownItAnchor.umd.js", "mangle": {"regex": "^_"}, "runkitExampleFilename": "runkit.js"}