{"version": 3, "file": "markdownItAnchor.modern.js", "sources": ["../permalink.js", "../index.js"], "sourcesContent": ["let emittedWarning = false\n\nconst position = {\n  false: 'push',\n  true: 'unshift',\n  after: 'push',\n  before: 'unshift'\n}\n\nconst permalinkSymbolMeta = {\n  isPermalinkSymbol: true\n}\n\nexport function legacy (slug, opts, state, idx) {\n  if (!emittedWarning) {\n    const warningText = 'Using deprecated markdown-it-anchor permalink option, see https://github.com/valeriangalliat/markdown-it-anchor#todo-anchor-or-file'\n\n    if (typeof process === 'object' && process && process.emitWarning) {\n      process.emitWarning(warningText)\n    } else {\n      console.warn(warningText)\n    }\n\n    emittedWarning = true\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.permalinkClass ? [['class', opts.permalinkClass]] : []),\n        ['href', opts.permalinkHref(slug, state)],\n        ...Object.entries(opts.permalinkAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_block', '', 0), { content: opts.permalinkSymbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.permalinkSpace) {\n    state.tokens[idx + 1].children[position[opts.permalinkBefore]](Object.assign(new state.Token('text', '', 0), { content: ' ' }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.permalinkBefore]](...linkTokens)\n}\n\nexport function renderHref (slug) {\n  return `#${slug}`\n}\n\nexport function renderAttrs (slug) {\n  return {}\n}\n\nconst commonDefaults = {\n  class: 'header-anchor',\n  symbol: '#',\n  renderHref,\n  renderAttrs\n}\n\nexport function makePermalink (renderPermalinkImpl) {\n  function renderPermalink (opts) {\n    opts = Object.assign({}, renderPermalink.defaults, opts)\n\n    return (slug, anchorOpts, state, idx) => {\n      return renderPermalinkImpl(slug, opts, anchorOpts, state, idx)\n    }\n  }\n\n  renderPermalink.defaults = Object.assign({}, commonDefaults)\n  renderPermalink.renderPermalinkImpl = renderPermalinkImpl\n\n  return renderPermalink\n}\n\nexport const linkInsideHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...(opts.ariaHidden ? [['aria-hidden', 'true']] : []),\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_inline', '', 0), { content: opts.symbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.space) {\n    const space = typeof opts.space === 'string' ? opts.space : ' '\n    const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n    state.tokens[idx + 1].children[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.placement]](...linkTokens)\n})\n\nObject.assign(linkInsideHeader.defaults, {\n  space: true,\n  placement: 'after',\n  ariaHidden: false\n})\n\nexport const ariaHidden = makePermalink(linkInsideHeader.renderPermalinkImpl)\n\nariaHidden.defaults = Object.assign({}, linkInsideHeader.defaults, {\n  ariaHidden: true\n})\n\nexport const headerLink = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    ...(opts.safariReaderFix ? [new state.Token('span_open', 'span', 1)] : []),\n    ...state.tokens[idx + 1].children,\n    ...(opts.safariReaderFix ? [new state.Token('span_close', 'span', -1)] : []),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  state.tokens[idx + 1] = Object.assign(new state.Token('inline', '', 0), {\n    children: linkTokens\n  })\n})\n\nObject.assign(headerLink.defaults, {\n  safariReaderFix: false\n})\n\nexport const linkAfterHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  if (!['visually-hidden', 'aria-label', 'aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called with unknown style option \\`${opts.style}\\``)\n  }\n\n  if (!['aria-describedby', 'aria-labelledby'].includes(opts.style) && !opts.assistiveText) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called without the \\`assistiveText\\` option in \\`${opts.style}\\` style`)\n  }\n\n  if (opts.style === 'visually-hidden' && !opts.visuallyHiddenClass) {\n    throw new Error('`permalink.linkAfterHeader` called without the `visuallyHiddenClass` option in `visually-hidden` style')\n  }\n\n  const title = state.tokens[idx + 1]\n    .children\n    .filter(token => token.type === 'text' || token.type === 'code_inline')\n    .reduce((acc, t) => acc + t.content, '')\n\n  const subLinkTokens = []\n  const linkAttrs = []\n\n  if (opts.class) {\n    linkAttrs.push(['class', opts.class])\n  }\n\n  linkAttrs.push(['href', opts.renderHref(slug, state)])\n  linkAttrs.push(...Object.entries(opts.renderAttrs(slug, state)))\n\n  if (opts.style === 'visually-hidden') {\n    subLinkTokens.push(\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['class', opts.visuallyHiddenClass]],\n      }),\n      Object.assign(new state.Token('text', '', 0), {\n        content: opts.assistiveText(title)\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n\n    if (opts.space) {\n      const space = typeof opts.space === 'string' ? opts.space : ' '\n      const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n      subLinkTokens[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n    }\n\n    subLinkTokens[position[opts.placement]](\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['aria-hidden', 'true']],\n      }),\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n  } else {\n    subLinkTokens.push(\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      })\n    )\n  }\n\n  if (opts.style === 'aria-label') {\n    linkAttrs.push(['aria-label', opts.assistiveText(title)])\n  } else if (['aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    linkAttrs.push([opts.style, slug])\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: linkAttrs\n    }),\n    ...subLinkTokens,\n    new state.Token('link_close', 'a', -1),\n  ]\n\n  state.tokens.splice(idx + 3, 0, ...linkTokens)\n\n  if (opts.wrapper) {\n    state.tokens.splice(idx, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[0] + '\\n'\n    }))\n\n    state.tokens.splice(idx + 3 + linkTokens.length + 1, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[1] + '\\n'\n    }))\n  }\n})\n\nObject.assign(linkAfterHeader.defaults, {\n  style: 'visually-hidden',\n  space: true,\n  placement: 'after',\n  wrapper: null\n})\n", "import * as permalink from './permalink'\n\nconst slugify = (s) => encodeURIComponent(String(s).trim().toLowerCase().replace(/\\s+/g, '-'))\n\nfunction getTokensText (tokens) {\n  return tokens\n    .filter(t => ['text', 'code_inline'].includes(t.type))\n    .map(t => t.content)\n    .join('')\n}\n\nfunction uniqueSlug (slug, slugs, failOnNonUnique, startIndex) {\n  let uniq = slug\n  let i = startIndex\n\n  if (failOnNonUnique && Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n    throw new Error(`User defined \\`id\\` attribute \\`${slug}\\` is not unique. Please fix it in your Markdown to continue.`)\n  } else {\n    while (Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n      uniq = `${slug}-${i}`\n      i += 1\n    }\n  }\n\n  slugs[uniq] = true\n\n  return uniq\n}\n\nconst isLevelSelectedNumber = selection => level => level >= selection\nconst isLevelSelectedArray = selection => level => selection.includes(level)\n\nfunction anchor (md, opts) {\n  opts = Object.assign({}, anchor.defaults, opts)\n\n  md.core.ruler.push('anchor', state => {\n    const slugs = {}\n    const tokens = state.tokens\n\n    const isLevelSelected = Array.isArray(opts.level)\n      ? isLevelSelectedArray(opts.level)\n      : isLevelSelectedNumber(opts.level)\n\n    for (let idx = 0; idx < tokens.length; idx++) {\n      const token = tokens[idx]\n\n      if (token.type !== 'heading_open') {\n        continue\n      }\n\n      if (!isLevelSelected(Number(token.tag.substr(1)))) {\n        continue\n      }\n\n      // Aggregate the next token children text.\n      const title = opts.getTokensText(tokens[idx + 1].children)\n\n      let slug = token.attrGet('id')\n\n      if (slug == null) {\n        slug = uniqueSlug(opts.slugify(title), slugs, false, opts.uniqueSlugStartIndex)\n      } else {\n        slug = uniqueSlug(slug, slugs, true, opts.uniqueSlugStartIndex)\n      }\n\n      token.attrSet('id', slug)\n\n      if (opts.tabIndex !== false) {\n        token.attrSet('tabindex', `${opts.tabIndex}`)\n      }\n\n      if (typeof opts.permalink === 'function') {\n        opts.permalink(slug, opts, state, idx)\n      } else if (opts.permalink) {\n        opts.renderPermalink(slug, opts, state, idx)\n      } else if (opts.renderPermalink && opts.renderPermalink !== permalink.legacy) {\n        opts.renderPermalink(slug, opts, state, idx)\n      }\n\n      // A permalink renderer could modify the `tokens` array so\n      // make sure to get the up-to-date index on each iteration.\n      idx = tokens.indexOf(token)\n\n      if (opts.callback) {\n        opts.callback(token, { slug, title })\n      }\n    }\n  })\n}\n\nanchor.permalink = permalink\n\nanchor.defaults = {\n  level: 1,\n  slugify,\n  uniqueSlugStartIndex: 1,\n  tabIndex: '-1',\n  getTokensText,\n\n  // Legacy options.\n  permalink: false,\n  renderPermalink: permalink.legacy,\n  permalinkClass: permalink.ariaHidden.defaults.class,\n  permalinkSpace: permalink.ariaHidden.defaults.space,\n  permalinkSymbol: '¶',\n  permalinkBefore: permalink.ariaHidden.defaults.placement === 'before',\n  permalinkHref: permalink.ariaHidden.defaults.renderHref,\n  permalinkAttrs: permalink.ariaHidden.defaults.renderAttrs\n}\n\n// Dirty hack to make `import anchor from 'markdown-it-anchor'` work with\n// TypeScript which doesn't support the `module` field of `package.json` and\n// will always get the CommonJS version which otherwise wouldn't have a\n// `default` key, resulting in markdown-it-anchor being undefined when being\n// imported that way.\nanchor.default = anchor\n\nexport default anchor\n"], "names": ["false", "true", "after", "before", "isPermalinkSymbol", "slug", "opts", "state", "idx", "emitted<PERSON><PERSON>ning", "process", "emitWarning", "warningText", "console", "warn", "Object", "assign", "Token", "attrs", "permalinkClass", "permalinkHref", "entries", "permalinkAttrs", "content", "permalinkSymbol", "meta", "permalinkSymbolMeta", "permalinkSpace", "tokens", "children", "position", "permalinkBefore", "linkTokens", "class", "symbol", "renderHref", "renderAttrs", "renderPermalinkImpl", "renderPermalink", "defaults", "anchorOpts", "commonDefaults", "makePermalink", "ariaHidden", "space", "placement", "linkInsideHeader", "safariReaderFix", "headerLink", "includes", "style", "assistiveText", "visuallyHiddenClass", "filter", "token", "type", "reduce", "acc", "t", "linkAttrs", "push", "subLinkTokens", "title", "splice", "wrapper", "length", "slugs", "failOnNonUnique", "startIndex", "prototype", "hasOwnProperty", "call", "uniq", "i", "md", "anchor", "core", "ruler", "Array", "isArray", "level", "selection", "isLevelSelectedNumber", "isLevelSelected", "Number", "tag", "substr", "getTokensText", "attrGet", "uniqueSlug", "slugify", "uniqueSlugStartIndex", "attrSet", "tabIndex", "permalink", "indexOf", "callback", "linkAfterHeader", "s", "encodeURIComponent", "String", "trim", "toLowerCase", "replace", "map", "join", "default"], "mappings": "AAAA,OAAqB,EAErB,QAAiB,CACfA,MAAO,OACPC,KAAM,UACNC,MAAO,OACPC,OAAQ,aAGkB,CAC1BC,mBAAmB,cAGGC,EAAMC,EAAMC,EAAOC,GACzC,IAAKC,EAAgB,CACnB,QAAoB,sIAEG,0BAAYC,SAAWA,QAAQC,YACpDD,QAAQC,YAAYC,GAEpBC,QAAQC,KAAKF,GAGfH,GAAiB,EAGnB,QAAmB,CACjBM,OAAOC,OAAO,MAAUC,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAKa,eAAiB,CAAC,CAAC,QAASb,EAAKa,iBAAmB,GAC7D,CAAC,OAAQb,EAAKc,cAAcf,EAAME,OAC/BQ,OAAOM,QAAQf,EAAKgB,eAAejB,EAAME,OAGhDQ,OAAOC,OAAO,MAAUC,MAAM,aAAc,GAAI,GAAI,CAAEM,QAASjB,EAAKkB,gBAAiBC,KAAMC,IAC3F,MAAUT,MAAM,aAAc,KAAM,IAGlCX,EAAKqB,gBACPpB,EAAMqB,OAAOpB,EAAM,GAAGqB,SAASC,EAASxB,EAAKyB,kBAAkBhB,OAAOC,OAAO,MAAUC,MAAM,OAAQ,GAAI,GAAI,CAAEM,QAAS,OAG1HhB,EAAMqB,OAAOpB,EAAM,GAAGqB,SAASC,EAASxB,EAAKyB,qBAAqBC,cAGxC3B,GAC1B,MAAQ,IAAGA,aAGgBA,GAC3B,MAAO,GAGT,QAAuB,CACrB4B,MAAO,gBACPC,OAAQ,IACRC,WAAAA,EACAC,YAAAA,cAG6BC,GAC7B,WAA0B/B,GAGxB,OAFAA,EAAOS,OAAOC,OAAO,GAAIsB,EAAgBC,SAAUjC,GAE5C,CAACD,EAAMmC,EAAYjC,EAAOC,MACJH,EAAMC,EAAMkC,EAAYjC,EAAOC,GAO9D,OAHA8B,EAAgBC,SAAWxB,OAAOC,OAAO,GAAIyB,GAC7CH,EAAgBD,oBAAsBA,YAKRK,EAAc,CAACrC,EAAMC,EAAMkC,EAAYjC,EAAOC,KAC5E,QAAmB,CACjBO,OAAOC,OAAO,MAAUC,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAK2B,MAAQ,CAAC,CAAC,QAAS3B,EAAK2B,QAAU,GAC3C,CAAC,OAAQ3B,EAAK6B,WAAW9B,EAAME,OAC3BD,EAAKqC,WAAa,CAAC,CAAC,cAAe,SAAW,MAC/C5B,OAAOM,QAAQf,EAAK8B,YAAY/B,EAAME,OAG7CQ,OAAOC,OAAO,MAAUC,MAAM,cAAe,GAAI,GAAI,CAAEM,QAASjB,EAAK4B,OAAQT,KAAMC,IACnF,MAAUT,MAAM,aAAc,KAAM,IAGtC,GAAIX,EAAKsC,MAAO,CACd,QAAoC,mBAAVA,MAAqBtC,EAAKsC,MAAQ,IAE5DrC,EAAMqB,OAAOpB,EAAM,GAAGqB,SAASC,EAASxB,EAAKuC,YAAY9B,OAAOC,OAAO,MAAUC,MAD9C,mBAAV2B,MAAqB,cAAgB,OAC+B,GAAI,GAAI,CAAErB,QAASqB,KAGlHrC,EAAMqB,OAAOpB,EAAM,GAAGqB,SAASC,EAASxB,EAAKuC,eAAeb,KAG9DjB,OAAOC,OAAO8B,EAAiBP,SAAU,CACvCK,OAAO,EACPC,UAAW,QACXF,YAAY,YAGYD,EAAcI,EAAiBT,qBAEzDM,EAAWJ,SAAWxB,OAAOC,OAAO,GAAI8B,EAAiBP,SAAU,CACjEI,YAAY,YAGYD,EAAc,CAACrC,EAAMC,EAAMkC,EAAYjC,EAAOC,KACtE,QAAmB,CACjBO,OAAOC,OAAO,MAAUC,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAK2B,MAAQ,CAAC,CAAC,QAAS3B,EAAK2B,QAAU,GAC3C,CAAC,OAAQ3B,EAAK6B,WAAW9B,EAAME,OAC5BQ,OAAOM,QAAQf,EAAK8B,YAAY/B,EAAME,UAGzCD,EAAKyC,gBAAkB,CAAC,MAAU9B,MAAM,YAAa,OAAQ,IAAM,MACpEV,EAAMqB,OAAOpB,EAAM,GAAGqB,YACrBvB,EAAKyC,gBAAkB,CAAC,MAAU9B,MAAM,aAAc,QAAS,IAAM,GACzE,MAAUA,MAAM,aAAc,KAAM,IAGtCV,EAAMqB,OAAOpB,EAAM,GAAKO,OAAOC,OAAO,MAAUC,MAAM,SAAU,GAAI,GAAI,CACtEY,SAAUG,MAIdjB,OAAOC,OAAOgC,EAAWT,SAAU,CACjCQ,iBAAiB,YAGYL,EAAc,CAACrC,EAAMC,EAAMkC,EAAYjC,EAAOC,KAC3E,IAAK,CAAC,kBAAmB,aAAc,mBAAoB,mBAAmByC,SAAS3C,EAAK4C,OAC1F,gBAAiB,oEAAmE5C,EAAK4C,WAG3F,IAAK,CAAC,mBAAoB,mBAAmBD,SAAS3C,EAAK4C,SAAW5C,EAAK6C,cACzE,gBAAiB,kFAAiF7C,EAAK4C,iBAGzG,GAAmB,oBAAf5C,EAAK4C,QAAgC5C,EAAK8C,oBAC5C,gBAAgB,0GAGlB,QAAc7C,EAAMqB,OAAOpB,EAAM,GAC9BqB,SACAwB,OAAOC,GAAwB,SAAfA,EAAMC,MAAkC,gBAAfD,EAAMC,MAC/CC,OAAO,CAACC,EAAKC,IAAMD,EAAMC,EAAEnC,QAAS,MAEjB,KACJ,GASlB,GAPIjB,EAAK2B,OACP0B,EAAUC,KAAK,CAAC,QAAStD,EAAK2B,QAGhC0B,EAAUC,KAAK,CAAC,OAAQtD,EAAK6B,WAAW9B,EAAME,KAC9CoD,EAAUC,QAAQ7C,OAAOM,QAAQf,EAAK8B,YAAY/B,EAAME,KAErC,oBAAfD,EAAK4C,MAA6B,CAWpC,GAVAW,EAAcD,KACZ7C,OAAOC,OAAO,MAAUC,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,QAASZ,EAAK8C,wBAEzBrC,OAAOC,OAAO,MAAUC,MAAM,OAAQ,GAAI,GAAI,CAC5CM,QAASjB,EAAK6C,cAAcW,KAE9B,MAAU7C,MAAM,aAAc,QAAS,IAGrCX,EAAKsC,MAAO,CACd,QAAoC,mBAAVA,MAAqBtC,EAAKsC,MAAQ,IAE5DiB,EAAc/B,EAASxB,EAAKuC,YAAY9B,OAAOC,OAAO,MAAUC,MAD7B,mBAAV2B,MAAqB,cAAgB,OACc,GAAI,GAAI,CAAErB,QAASqB,KAGjGiB,EAAc/B,EAASxB,EAAKuC,YAC1B9B,OAAOC,OAAO,MAAUC,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,cAAe,WAE1BH,OAAOC,OAAO,MAAUC,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASjB,EAAK4B,OACdT,KAAMC,IAER,MAAUT,MAAM,aAAc,QAAS,SAGzC4C,EAAcD,KACZ7C,OAAOC,OAAO,MAAUC,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASjB,EAAK4B,OACdT,KAAMC,KAKO,eAAfpB,EAAK4C,MACPS,EAAUC,KAAK,CAAC,aAActD,EAAK6C,cAAcW,KACxC,CAAC,mBAAoB,mBAAmBb,SAAS3C,EAAK4C,QAC/DS,EAAUC,KAAK,CAACtD,EAAK4C,MAAO7C,IAG9B,QAAmB,CACjBU,OAAOC,OAAO,MAAUC,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAOyC,OAENE,EACH,MAAU5C,MAAM,aAAc,KAAM,IAGtCV,EAAMqB,OAAOmC,OAAOvD,EAAM,EAAG,KAAMwB,GAE/B1B,EAAK0D,UACPzD,EAAMqB,OAAOmC,OAAOvD,EAAK,EAAGO,OAAOC,OAAO,MAAUC,MAAM,aAAc,GAAI,GAAI,CAC9EM,QAASjB,EAAK0D,QAAQ,GAAK,QAG7BzD,EAAMqB,OAAOmC,OAAOvD,EAAM,EAAIwB,EAAWiC,OAAS,EAAG,EAAGlD,OAAOC,OAAO,MAAUC,MAAM,aAAc,GAAI,GAAI,CAC1GM,QAASjB,EAAK0D,QAAQ,GAAK,WCjNjC,WAAqB3D,EAAM6D,EAAOC,EAAiBC,GACjD,MAAW/D,IACH+D,EAER,GAAID,GAAmBpD,OAAOsD,UAAUC,eAAeC,KAAKL,EAAOM,GACjE,gBAAiB,mCAAkCnE,kEAEnD,KAAOU,OAAOsD,UAAUC,eAAeC,KAAKL,EAAOM,IACjDA,EAAQ,GAAEnE,KAAQoE,IAClBA,GAAK,EAMT,OAFAP,EAAMM,IAAQ,IAQhB,WAAiBE,EAAIpE,GACnBA,EAAOS,OAAOC,OAAO,GAAI2D,EAAOpC,SAAUjC,GAE1CoE,EAAGE,KAAKC,MAAMjB,KAAK,SAAUrD,IAC3B,QAAc,KACCA,EAAMqB,SAEGkD,MAAMC,QAAQzE,EAAK0E,QATlBC,EAUA3E,EAAK0E,MAVQA,GAASC,EAAUhC,SAAS+B,IADxCC,CAAAA,GAAaD,GAASA,GAASC,EAYrDC,CAAsB5E,EAAK0E,OAXNC,IAAAA,EAazB,IAAK,MAAU,EAAGzE,EAAMoB,EAAOqC,OAAQzD,IAAO,CAC5C,QAAcoB,EAAOpB,GAErB,GAAmB,iBAAf8C,EAAMC,KACR,SAGF,IAAK4B,EAAgBC,OAAO9B,EAAM+B,IAAIC,OAAO,KAC3C,SAIF,QAAchF,EAAKiF,cAAc3D,EAAOpB,EAAM,GAAGqB,UAEjD,MAAWyB,EAAMkC,QAAQ,MAGvBnF,EADU,MAARA,EACKoF,EAAWnF,EAAKoF,QAAQ5B,GAAQI,GAAO,EAAO5D,EAAKqF,sBAEnDF,EAAWpF,EAAM6D,GAAO,EAAM5D,EAAKqF,sBAG5CrC,EAAMsC,QAAQ,KAAMvF,IAEE,IAAlBC,EAAKuF,UACPvC,EAAMsC,QAAQ,WAAa,GAAEtF,EAAKuF,UAGN,qBAAdC,UACdxF,EAAKwF,UAAUzF,EAAMC,EAAMC,EAAOC,IACzBF,EAAKwF,WAELxF,EAAKgC,iBAAmBhC,EAAKgC,kBAAoBwD,IAD1DxF,EAAKgC,gBAAgBjC,EAAMC,EAAMC,EAAOC,GAO1CA,EAAMoB,EAAOmE,QAAQzC,GAEjBhD,EAAK0F,UACP1F,EAAK0F,SAAS1C,EAAO,CAAEjD,KAAAA,EAAMyD,MAAAA,OD6IrC/C,OAAOC,OAAOiF,EAAgB1D,SAAU,CACtCW,MAAO,kBACPN,OAAO,EACPC,UAAW,QACXmB,QAAS,OC3IXW,EAAOmB,8IAEPnB,EAAOpC,SAAW,CAChByC,MAAO,EACPU,QA5FeQ,GAAMC,mBAAmBC,OAAOF,GAAGG,OAAOC,cAAcC,QAAQ,OAAQ,MA6FvFZ,qBAAsB,EACtBE,SAAU,KACVN,cA7FF,SAAwB3D,GACtB,SACGyB,OAAOK,GAAK,CAAC,OAAQ,eAAeT,SAASS,EAAEH,OAC/CiD,IAAI9C,GAAKA,EAAEnC,SACXkF,KAAK,KA4FRX,WAAW,EACXxD,gBAAiBwD,EACjB3E,eAAgB2E,EAAqBvD,SAASN,MAC9CN,eAAgBmE,EAAqBvD,SAASK,MAC9CpB,gBAAiB,IACjBO,gBAA6D,WAA5C+D,EAAqBvD,SAASM,UAC/CzB,cAAe0E,EAAqBvD,SAASJ,WAC7Cb,eAAgBwE,EAAqBvD,SAASH,aAQhDuC,EAAO+B,QAAU/B"}