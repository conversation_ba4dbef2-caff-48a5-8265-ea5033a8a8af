{"name": "@11ty/eleventy-utils", "version": "1.0.3", "description": "Low level internal utilities to be shared amongst Eleventy projects", "main": "index.js", "files": ["src", "src/**", "index.js", "!test", "!test/**"], "scripts": {"test": "npx ava --verbose"}, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "keywords": ["eleventy"], "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>", "email": "zach<PERSON><PERSON><EMAIL>", "url": "https://zachleat.com/"}, "repository": {"type": "git", "url": "git://github.com/11ty/eleventy-utils.git"}, "bugs": "https://github.com/11ty/eleventy-utils/issues", "homepage": "https://github.com/11ty/eleventy-utils/", "dependencies": {"normalize-path": "^3.0.0"}, "devDependencies": {"ava": "^6.1.3"}}