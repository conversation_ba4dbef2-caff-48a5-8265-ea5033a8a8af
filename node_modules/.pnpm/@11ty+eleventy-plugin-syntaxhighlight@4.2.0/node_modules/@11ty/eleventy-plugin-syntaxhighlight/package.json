{"name": "@11ty/eleventy-plugin-syntaxhighlight", "version": "4.2.0", "description": "Prism.js based syntax highlighting for Markdown, Liquid, Nunjucks, and 11ty.js templates.", "publishConfig": {"access": "public"}, "main": ".eleventy.js", "scripts": {"test": "npx ava", "demo": "npx @11ty/eleventy --input=demo --output=demo/_site --config=demo/eleventy-config.js", "start": "npx @11ty/eleventy --input=demo --output=demo/_site --config=demo/eleventy-config.js --serve"}, "repository": {"type": "git", "url": "git+https://github.com/11ty/eleventy-plugin-syntaxhighlight.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "keywords": ["eleventy", "eleventy-plugin"], "author": {"name": "<PERSON>", "email": "zach<PERSON><PERSON><EMAIL>", "url": "https://zachleat.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/11ty/eleventy-plugin-syntaxhighlight/issues"}, "homepage": "https://www.11ty.dev/docs/plugins/syntaxhighlight/", "11ty": {"compatibility": ">=0.5.4"}, "devDependencies": {"@11ty/eleventy": "^1.0.2", "ava": "^5.0.1", "liquidjs": "^9.42.1", "markdown-it": "^13.0.1"}, "dependencies": {"linkedom": "^0.14.19", "prismjs": "^1.29.0"}, "ava": {"environmentVariables": {}, "failFast": false, "files": ["./test/*.js"]}}