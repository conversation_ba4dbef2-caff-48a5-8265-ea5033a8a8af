<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="../test.css">
    <link rel="stylesheet" href="../prism-theme.css">
  </head>
  <body>

```ts
function myFunction() {
  return true;
}
```

```typescript
function myFunction() {
  return true;
}
```

```js
function myFunction() {
  return true;
}
```

```js
let multilineString = `
  this is the first line
  this is the middle line
  this is the last line
`;
```

## Dash line

```js/-
let multilineString = `
  this is the first line
  this is the middle line
  this is the last line
`;
```

```js/1,3
let multilineString = `
  this is the first line
  this is the middle line
  this is the last line
`;
```

## Scrollbar

```js
import { aReallyLongFunctionNameThatCouldBeLongerButThisShouldBeLongEnoughByNowHopefully as anEvenLongerFunctionNameWithMoreCharactersThanCouldBeImaginedByAnyOnePersonInThisEntireWorldOfPeopleThatOneMightKnowAtLeastThatIsWhatIsTheorizedByThisLongName } from 'wow-this-is-so-long-you-might-need-a-scrollbar-to-see-it.long-ol-file-extension-that-should-not-be-this-long-on-a-real-site-but-this-is-to-demonstrate-the-accessibility-of-tabindex-and-scrollbars.js';
```

  </body>
</html>
