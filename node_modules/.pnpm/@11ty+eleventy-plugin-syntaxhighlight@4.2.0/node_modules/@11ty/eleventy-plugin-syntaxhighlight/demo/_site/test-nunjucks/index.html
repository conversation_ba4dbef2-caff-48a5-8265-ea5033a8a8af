<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="../test.css">
    <link rel="stylesheet" href="../prism-theme.css">
  </head>
  <body>

<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">function</span> <span class="token function">myFunction</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span><br><span class="token punctuation">}</span></code></pre>

<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">let</span> multilineString <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br>  this is the first line<br>  this is the middle line<br>  this is the last line<br></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span></code></pre>

<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">let</span> multilineString <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br><mark class="highlight-line highlight-line-active">  this is the first line</mark><br><span class="highlight-line">  this is the middle line</span><br><mark class="highlight-line highlight-line-active">  this is the last line</mark><br></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span></code></pre>

<pre class="language-js" tabindex="0"><code class="language-js"><span class="highlight-line"><span class="token function">alert</span><span class="token punctuation">(</span><span class="token string">"test"</span><span class="token punctuation">)</span><span class="token punctuation">;</span></span><br><mark class="highlight-line highlight-line-active"></mark><br><span class="token keyword">let</span> multilineString <span class="token operator">=</span> <span class="token function">buildSchema</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br><mark class="highlight-line highlight-line-active">  this is the first line</mark><br><span class="highlight-line">  this is the middle line</span><br><span class="highlight-line">  this is the last line</span><br></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">;</span><br><span class="highlight-line"></span><br><span class="highlight-line"><span class="token function">alert</span><span class="token punctuation">(</span><span class="token string">"test"</span><span class="token punctuation">)</span><span class="token punctuation">;</span></span></code></pre>

<pre class="language-js" tabindex="0"><code class="language-js">module<span class="token punctuation">.</span><span class="token function-variable function">exports</span> <span class="token operator">=</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter"><span class="token punctuation">{</span>collections<span class="token punctuation">}</span></span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">&lt;ul><br></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>collections<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">post</span><span class="token punctuation">)</span> <span class="token operator">=></span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">&lt;li></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span> post<span class="token punctuation">.</span>data<span class="token punctuation">.</span>title <span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">&lt;/li></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">"\n"</span><span class="token punctuation">)</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"><br>&lt;/ul></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span><br><span class="token punctuation">}</span><span class="token punctuation">;</span></code></pre>

<pre class="language-js" tabindex="0"><code class="language-js"><span class="highlight-line">module<span class="token punctuation">.</span><span class="token function-variable function">exports</span> <span class="token operator">=</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter"><span class="token punctuation">{</span>collections<span class="token punctuation">}</span></span><span class="token punctuation">)</span> <span class="token punctuation">{</span></span><br>  <span class="token keyword">return</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">&lt;ul><br><span class="highlight-line"></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>collections<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">post</span><span class="token punctuation">)</span> <span class="token operator">=></span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">&lt;li></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span> post<span class="token punctuation">.</span>data<span class="token punctuation">.</span>title <span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">&lt;/li></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">"\n"</span><span class="token punctuation">)</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"></span><br>&lt;/ul></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span><br><span class="highlight-line"><span class="token punctuation">}</span><span class="token punctuation">;</span></span></code></pre>

<pre class="language-typescript" tabindex="0"><code class="language-typescript"><span class="token keyword">function</span> <span class="token function">myFunction</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span><br><span class="token punctuation">}</span></code></pre>

<pre class="language-ts" tabindex="0"><code class="language-ts"><span class="token keyword">function</span> <span class="token function">myFunction</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span><br><span class="token punctuation">}</span></code></pre>
  </body>
</html>
