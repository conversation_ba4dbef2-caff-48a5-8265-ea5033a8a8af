<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="../test.css">
    <link rel="stylesheet" href="../prism-theme.css">
  </head>
  <body>
<pre class="language-ts" tabindex="0"><code class="language-ts"><span class="token keyword">function</span> <span class="token function">myFunction</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span><br><span class="token punctuation">}</span></code></pre>
<pre class="language-typescript" tabindex="0"><code class="language-typescript"><span class="token keyword">function</span> <span class="token function">myFunction</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span><br><span class="token punctuation">}</span></code></pre>
<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">function</span> <span class="token function">myFunction</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><br>  <span class="token keyword">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span><br><span class="token punctuation">}</span></code></pre>
<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">let</span> multilineString <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br>  this is the first line<br>  this is the middle line<br>  this is the last line<br></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span></code></pre>
<h2>Dash line</h2>
<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">let</span> multilineString <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br><span class="highlight-line">  this is the first line</span><br><span class="highlight-line">  this is the middle line</span><br><span class="highlight-line">  this is the last line</span><br></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span></code></pre>
<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">let</span> multilineString <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br><mark class="highlight-line highlight-line-active">  this is the first line</mark><br><span class="highlight-line">  this is the middle line</span><br><mark class="highlight-line highlight-line-active">  this is the last line</mark><br></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span></code></pre>
<h2>Scrollbar</h2>
<pre class="language-js" tabindex="0"><code class="language-js"><span class="token keyword">import</span> <span class="token punctuation">{</span> aReallyLongFunctionNameThatCouldBeLongerButThisShouldBeLongEnoughByNowHopefully <span class="token keyword">as</span> anEvenLongerFunctionNameWithMoreCharactersThanCouldBeImaginedByAnyOnePersonInThisEntireWorldOfPeopleThatOneMightKnowAtLeastThatIsWhatIsTheorizedByThisLongName <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">'wow-this-is-so-long-you-might-need-a-scrollbar-to-see-it.long-ol-file-extension-that-should-not-be-this-long-on-a-real-site-but-this-is-to-demonstrate-the-accessibility-of-tabindex-and-scrollbars.js'</span><span class="token punctuation">;</span></code></pre>
  </body>
</html>
