on:
  push:
    branches-ignore:
      - "gh-pages"
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: ["ubuntu-latest", "macos-latest", "windows-latest"]
        node: ["14", "16", "18"]
    name: Node.js ${{ matrix.node }} on ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node }}
          # cache: npm
      - run: npm install
      - run: npm test
env:
  YARN_GPG: no
