<p align="center"><img src="https://www.11ty.dev/img/logo-github.svg" width="200" height="200" alt="11ty Logo"></p>

# eleventy-plugin-rss 🕚⚡️🎈🐀

A pack of [Eleventy](https://github.com/11ty/eleventy) filters for generating Atom, JSON and RSS feeds using the Nunjucks templating engine.


See `sample/feed.njk` for an example Atom feed template, `sample/feed.json` for an example JSON feed template, or `sample/feed-rss.njk` for an example RSS feed template.

## [The full `eleventy-plugin-rss` documentation is on 11ty.dev](https://www.11ty.dev/docs/plugins/rss/).

* _This is a plugin for the [Eleventy static site generator](https://www.11ty.dev/)._
* Find more [Eleventy plugins](https://www.11ty.dev/docs/plugins/).
* Please star [Eleventy on GitHub](https://github.com/11ty/eleventy/), follow [@eleven_ty](https://twitter.com/eleven_ty) on Twitter, and support [11ty on Open Collective](https://opencollective.com/11ty)

[![npm Version](https://img.shields.io/npm/v/@11ty/eleventy-plugin-rss.svg?style=for-the-badge)](https://www.npmjs.com/package/@11ty/eleventy-plugin-rss) [![GitHub issues](https://img.shields.io/github/issues/11ty/eleventy-plugin-rss.svg?style=for-the-badge)](https://github.com/11ty/eleventy-plugin-rss/issues)

## Installation

```
npm install @11ty/eleventy-plugin-rss
```

_[The full `eleventy-plugin-rss` documentation is on 11ty.dev](https://www.11ty.dev/docs/plugins/rss/)._

## Tests

```
npm run test
```

- We use the [ava JavaScript test runner](https://github.com/avajs/ava) ([Assertions documentation](https://github.com/avajs/ava/blob/master/docs/03-assertions.md))
- ℹ️ To keep tests fast, thou shalt try to avoid writing files in tests.

