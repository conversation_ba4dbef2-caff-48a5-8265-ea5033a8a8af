hoistPattern:
  - '*'
hoistedDependencies:
  '@11ty/dependency-tree@2.0.1':
    '@11ty/dependency-tree': private
  '@11ty/eleventy-dev-server@1.0.4':
    '@11ty/eleventy-dev-server': private
  '@11ty/eleventy-utils@1.0.3':
    '@11ty/eleventy-utils': private
  '@11ty/lodash-custom@4.17.21':
    '@11ty/lodash-custom': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@iarna/toml@2.2.5':
    '@iarna/toml': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@sindresorhus/slugify@1.1.2':
    '@sindresorhus/slugify': private
  '@sindresorhus/transliterate@0.1.2':
    '@sindresorhus/transliterate': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/minimatch@3.0.5':
    '@types/minimatch': private
  a-sync-waterfall@1.0.1:
    a-sync-waterfall: private
  acorn@7.4.1:
    acorn: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-differ@3.0.0:
    array-differ: private
  array-union@2.1.0:
    array-union: private
  array-uniq@1.0.3:
    array-uniq: private
  arrify@2.0.1:
    arrify: private
  asap@2.0.6:
    asap: private
  assert-never@1.4.0:
    assert-never: private
  async@3.2.6:
    async: private
  babel-walk@3.0.0-canary-5:
    babel-walk: private
  balanced-match@1.0.2:
    balanced-match: private
  bcp-47-match@1.0.3:
    bcp-47-match: private
  bcp-47-normalize@1.1.1:
    bcp-47-normalize: private
  bcp-47@1.0.8:
    bcp-47: private
  binary-extensions@2.3.0:
    binary-extensions: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001724:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  character-parser@2.2.0:
    character-parser: private
  chokidar@3.6.0:
    chokidar: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@10.0.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  constantinople@4.0.1:
    constantinople: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@5.1.0:
    css-select: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  cssom@0.5.0:
    cssom: private
  debug@4.4.1:
    debug: private
  dependency-graph@0.11.0:
    dependency-graph: private
  dev-ip@1.0.1:
    dev-ip: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  doctypes@1.1.0:
    doctypes: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.171:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  entities@4.5.0:
    entities: private
  errno@0.1.8:
    errno: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  esprima@4.0.1:
    esprima: private
  extend-shallow@2.0.1:
    extend-shallow: private
  fast-glob@3.3.3:
    fast-glob: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@11.3.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gray-matter@4.0.3:
    gray-matter: private
  hamljs@0.6.2:
    hamljs: private
  handlebars@4.7.8:
    handlebars: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  html-escaper@3.0.3:
    html-escaper: private
  htmlparser2@8.0.2:
    htmlparser2: private
  http-equiv-refresh@1.0.0:
    http-equiv-refresh: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-decimal@1.0.4:
    is-decimal: private
  is-expression@4.0.0:
    is-expression: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-json@2.0.1:
    is-json: private
  is-number@7.0.0:
    is-number: private
  is-promise@2.2.2:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  isexe@2.0.0:
    isexe: private
  iso-639-1@2.1.15:
    iso-639-1: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jiti@1.21.7:
    jiti: private
  js-stringify@1.0.2:
    js-stringify: private
  js-yaml@3.14.1:
    js-yaml: private
  jsonfile@6.1.0:
    jsonfile: private
  jstransformer@1.0.0:
    jstransformer: private
  junk@1.0.3:
    junk: private
  kind-of@6.0.3:
    kind-of: private
  kleur@4.1.5:
    kleur: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkedom@0.14.26:
    linkedom: private
  linkify-it@5.0.0:
    linkify-it: private
  liquidjs@10.21.1:
    liquidjs: private
  list-to-array@1.1.0:
    list-to-array: private
  lodash.deburr@4.1.0:
    lodash.deburr: private
  lru-cache@10.4.3:
    lru-cache: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  maximatch@0.1.0:
    maximatch: private
  mdurl@2.0.0:
    mdurl: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime@3.0.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mkdirp@0.5.6:
    mkdirp: private
  moo@0.5.2:
    moo: private
  morphdom@2.7.5:
    morphdom: private
  ms@2.1.3:
    ms: private
  multimatch@5.0.0:
    multimatch: private
  mustache@4.2.0:
    mustache: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  neo-async@2.6.2:
    neo-async: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  nth-check@2.1.1:
    nth-check: private
  nunjucks@3.2.4(chokidar@3.6.0):
    nunjucks: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parse-srcset@1.0.2:
    parse-srcset: private
  parseurl@1.3.3:
    parseurl: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  please-upgrade-node@3.2.0:
    please-upgrade-node: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@5.1.0(jiti@1.21.7)(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-reporter@7.1.0(postcss@8.5.6):
    postcss-reporter: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  posthtml-parser@0.11.0:
    posthtml-parser: private
  posthtml-render@3.0.0:
    posthtml-render: private
  posthtml-urls@1.0.0:
    posthtml-urls: private
  posthtml@0.16.6:
    posthtml: private
  pretty-hrtime@1.0.3:
    pretty-hrtime: private
  prismjs@1.30.0:
    prismjs: private
  promise-each@2.2.0:
    promise-each: private
  promise@7.3.1:
    promise: private
  prr@1.0.1:
    prr: private
  pug-attrs@3.0.0:
    pug-attrs: private
  pug-code-gen@3.0.3:
    pug-code-gen: private
  pug-error@2.1.0:
    pug-error: private
  pug-filters@4.0.0:
    pug-filters: private
  pug-lexer@5.0.1:
    pug-lexer: private
  pug-linker@4.0.0:
    pug-linker: private
  pug-load@3.0.0:
    pug-load: private
  pug-parser@6.0.0:
    pug-parser: private
  pug-runtime@3.0.1:
    pug-runtime: private
  pug-strip-comments@2.0.0:
    pug-strip-comments: private
  pug-walk@2.0.0:
    pug-walk: private
  pug@3.0.3:
    pug: private
  punycode.js@2.3.1:
    punycode.js: private
  queue-microtask@1.2.3:
    queue-microtask: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  recursive-copy@2.0.14:
    recursive-copy: private
  require-directory@2.1.1:
    require-directory: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@2.7.1:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  section-matter@1.0.0:
    section-matter: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  slash@5.1.0:
    slash: private
  slugify@1.6.6:
    slugify: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.6.1:
    source-map: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssri@8.0.1:
    ssri: private
  statuses@2.0.1:
    statuses: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom-string@1.0.0:
    strip-bom-string: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  thenby@1.3.4:
    thenby: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  token-stream@1.0.0:
    token-stream: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  uc.micro@2.1.0:
    uc.micro: private
  uglify-js@3.19.3:
    uglify-js: private
  uhyphen@0.2.0:
    uhyphen: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  util-deprecate@1.0.2:
    util-deprecate: private
  void-elements@3.1.0:
    void-elements: private
  which@2.0.2:
    which: private
  with@7.0.2:
    with: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.2:
    ws: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Sat, 21 Jun 2025 09:08:37 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - '@rollup/rollup-win32-x64-msvc@4.44.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
