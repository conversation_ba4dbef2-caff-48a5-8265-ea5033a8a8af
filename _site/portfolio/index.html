<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Our Portfolio | </title>
  <meta name="description" content="Discover how we&#39;ve helped businesses transform their operations and achieve remarkable results through innovative IT solutions.">
  
  <!-- Open Graph -->
  <meta property="og:title" content="Our Portfolio">
  <meta property="og:description" content="Discover how we&#39;ve helped businesses transform their operations and achieve remarkable results through innovative IT solutions.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="/portfolio/">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Our Portfolio">
  <meta name="twitter:description" content="Discover how we&#39;ve helped businesses transform their operations and achieve remarkable results through innovative IT solutions.">
  
  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body class="min-h-screen bg-white">
  <header class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <a href="/" class="flex items-center space-x-2">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">N</span>
        </div>
        <span class="text-2xl font-bold text-gray-900"></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        
      </nav>

      <div class="hidden md:flex items-center space-x-4">
        <a
          href="/contact/"
          class="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Get Started
        </a>
      </div>

      <!-- Mobile menu button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden hidden py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        
        <a
          href="/contact/"
          class="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
        >
          Get Started
        </a>
      </div>
    </div>
  </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script> 
  
  <main class="flex-1">
    
<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Our Portfolio
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Discover how we've helped businesses transform their operations and 
          achieve remarkable results through innovative IT solutions.
        </p>
      </div>
    </div>
  </section>

  <!-- Filter Section -->
  <section class="py-12 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-wrap justify-center gap-4 animate-slide-up">
        <button class="filter-btn active px-6 py-3 rounded-full font-medium transition-all duration-300 bg-primary-600 text-white shadow-lg" data-filter="all">
          All Projects
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="cloud">
          Cloud Solutions
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="security">
          Cybersecurity
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="transformation">
          Digital Transformation
        </button>
        <button class="filter-btn px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="development">
          Custom Development
        </button>
      </div>
    </div>
  </section>

  <!-- Projects Grid -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="portfolio-grid">
        
        
        
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: 0s;"
            data-category="cloud"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="E-commerce Cloud Migration"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>2024</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    AWS
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Migration
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    E-commerce
                  </span>
                
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                E-commerce Cloud Migration
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: RetailMax Inc.
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                Complete cloud migration for a growing e-commerce platform, reducing costs by 35% and improving performance.
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      35% cost reduction
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      99.9% uptime achieved
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      50% faster load times
                    </li>
                  
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: 0.1s;"
            data-category="security"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Financial Services Security Overhaul"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>2024</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Security
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Compliance
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Finance
                  </span>
                
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                Financial Services Security Overhaul
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: SecureBank Ltd.
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                Comprehensive cybersecurity implementation for a financial institution, ensuring compliance and protection.
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Zero security incidents
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      SOC 2 compliance achieved
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      24/7 monitoring implemented
                    </li>
                  
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: 0.2s;"
            data-category="transformation"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Manufacturing Digital Transformation"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>2024</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    IoT
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Automation
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Manufacturing
                  </span>
                
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                Manufacturing Digital Transformation
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: TechManufacturing Co.
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                Complete digital transformation of manufacturing processes, implementing IoT and automation solutions.
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      40% efficiency increase
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Real-time monitoring
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Predictive maintenance
                    </li>
                  
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: 0.30000000000000004s;"
            data-category="development"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Healthcare Management System"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>2023</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Healthcare
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Custom Development
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    HIPAA
                  </span>
                
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                Healthcare Management System
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: HealthCare Plus
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                Custom patient management system with integrated billing and appointment scheduling.
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      90% faster patient processing
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Integrated billing system
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Mobile accessibility
                    </li>
                  
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: 0.4s;"
            data-category="cloud"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/159888/pexels-photo-159888.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Multi-Cloud Infrastructure"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>2023</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Multi-cloud
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Disaster Recovery
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Backup
                  </span>
                
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                Multi-Cloud Infrastructure
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: GlobalTech Solutions
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                Multi-cloud infrastructure setup with disaster recovery and automated backup systems.
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Multi-cloud redundancy
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Automated backups
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Disaster recovery
                    </li>
                  
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div
            class="portfolio-item bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group animate-scale-in"
            style="animation-delay: 0.5s;"
            data-category="security"
          >
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/3184298/pexels-photo-3184298.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="EdTech Platform Security"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 right-4">
                <div class="flex items-center space-x-2 text-white text-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>2023</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex flex-wrap gap-2 mb-4">
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    EdTech
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Privacy
                  </span>
                
                  <span class="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    FERPA
                  </span>
                
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                EdTech Platform Security
              </h3>
              
              <p class="text-gray-600 text-sm mb-2 font-medium">
                Client: EduPlatform Inc.
              </p>
              
              <p class="text-gray-600 mb-4 leading-relaxed">
                Security implementation for online education platform protecting student data and privacy.
              </p>

              <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-3">Key Results:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Student data protection
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      FERPA compliance
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-2"></div>
                      Secure authentication
                    </li>
                  
                </ul>
              </div>

              <button class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center group/btn">
                View Case Study
                <svg class="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
              </button>
            </div>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-4">
          Project Success Metrics
        </h2>
        <p class="text-xl text-blue-100">
          Real results that matter to your business
        </p>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
        

        
          <div
            class="text-center animate-scale-in"
            style="animation-delay: 0s;"
          >
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">
              200+
            </div>
            <div class="text-blue-100 font-medium">Projects Completed</div>
          </div>
        
          <div
            class="text-center animate-scale-in"
            style="animation-delay: 0.1s;"
          >
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">
              98%
            </div>
            <div class="text-blue-100 font-medium">Client Satisfaction</div>
          </div>
        
          <div
            class="text-center animate-scale-in"
            style="animation-delay: 0.2s;"
          >
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">
              35%
            </div>
            <div class="text-blue-100 font-medium">Average Cost Savings</div>
          </div>
        
          <div
            class="text-center animate-scale-in"
            style="animation-delay: 0.30000000000000004s;"
          >
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">
              24/7
            </div>
            <div class="text-blue-100 font-medium">Support Coverage</div>
          </div>
        
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-6">
          Ready to Start Your Success Story?
        </h2>
        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
          Join our portfolio of successful clients. Let's discuss how we can
          help transform your business with innovative IT solutions.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-primary-700 transition-all duration-300 group"
        >
          Start Your Project
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const filterButtons = document.querySelectorAll('.filter-btn');
  const portfolioItems = document.querySelectorAll('.portfolio-item');

  filterButtons.forEach(button => {
    button.addEventListener('click', function() {
      const filter = this.getAttribute('data-filter');

      // Update active button
      filterButtons.forEach(btn => {
        btn.classList.remove('active', 'bg-primary-600', 'text-white', 'shadow-lg');
        btn.classList.add('bg-gray-100', 'text-gray-700');
      });

      this.classList.add('active', 'bg-primary-600', 'text-white', 'shadow-lg');
      this.classList.remove('bg-gray-100', 'text-gray-700');

      // Filter items
      portfolioItems.forEach(item => {
        const category = item.getAttribute('data-category');
        if (filter === 'all' || category === filter) {
          item.style.display = 'block';
          item.classList.add('animate-scale-in');
        } else {
          item.style.display = 'none';
        }
      });
    });
  });
});
</script>

  </main>
  
  <footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">N</span>
          </div>
          <span class="text-2xl font-bold"></span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md">
          Empowering SMEs with cutting-edge IT solutions. We transform businesses through 
          technology, delivering innovative services that drive growth and efficiency.
        </p>
        <div class="flex space-x-4">
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span></span>
          </div>
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span></span>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Services</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/services/cloud-solutions" class="hover:text-white transition-colors">Cloud Solutions</a></li>
          <li><a href="/services/cybersecurity" class="hover:text-white transition-colors">Cybersecurity</a></li>
          <li><a href="/services/digital-transformation" class="hover:text-white transition-colors">Digital Transformation</a></li>
          <li><a href="/services/it-consulting" class="hover:text-white transition-colors">IT Consulting</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Company</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
          <li><a href="/portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
          <li><a href="/blog" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-300 text-sm">
        © 2025 . All rights reserved.
      </p>
      <div class="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-300">
        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
      </div>
    </div>
  </div>
</footer>

<!-- Scroll to Top Button -->
<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 hidden"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const scrollToTopButton = document.getElementById('scroll-to-top');
  
  if (scrollToTopButton) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 400) {
        scrollToTopButton.classList.remove('hidden');
      } else {
        scrollToTopButton.classList.add('hidden');
      }
    });
    
    scrollToTopButton.addEventListener('click', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});
</script> 
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  
  
</body>
</html> 