// Animation utilities for the website
document.addEventListener('DOMContentLoaded', function () {
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
      }
    });
  }, observerOptions);

  // Observe elements with animation classes
  const animatedElements = document.querySelectorAll(
    '.animate-slide-up, .animate-scale-in, .animate-fade-in'
  );
  animatedElements.forEach(el => observer.observe(el));

  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    });
  });

  // Parallax effect for hero section
  const heroSection = document.querySelector('.bg-gradient-to-br');
  if (heroSection) {
    window.addEventListener('scroll', function () {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;
      heroSection.style.transform = `translateY(${rate}px)`;
    });
  }

  // Counter animation for metrics
  const counters = document.querySelectorAll(
    '.text-3xl.md\\:text-4xl.font-bold.text-primary-600'
  );
  const counterObserver = new IntersectionObserver(
    function (entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const counter = entry.target;
          const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
          const suffix = counter.textContent.replace(/[\d]/g, '');
          let current = 0;
          const increment = target / 50;

          const updateCounter = () => {
            if (current < target) {
              current += increment;
              counter.textContent = Math.ceil(current) + suffix;
              requestAnimationFrame(updateCounter);
            } else {
              counter.textContent = target + suffix;
            }
          };

          updateCounter();
          counterObserver.unobserve(counter);
        }
      });
    },
    { threshold: 0.5 }
  );

  counters.forEach(counter => counterObserver.observe(counter));
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  .animate-slide-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }
  
  .animate-slide-up.animate-in {
    opacity: 1;
    transform: translateY(0);
  }
  
  .animate-scale-in {
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }
  
  .animate-scale-in.animate-in {
    opacity: 1;
    transform: scale(1);
  }
  
  .animate-fade-in {
    opacity: 0;
    transition: opacity 0.6s ease;
  }
  
  .animate-fade-in.animate-in {
    opacity: 1;
  }
  
  /* Hover effects */
  .group:hover .group-hover\\:translate-x-1 {
    transform: translateX(0.25rem);
  }
  
  .group:hover .group-hover\\:translate-x-2 {
    transform: translateX(0.5rem);
  }
  
  .group:hover .group-hover\\:scale-110 {
    transform: scale(1.1);
  }
`;
document.head.appendChild(style);
