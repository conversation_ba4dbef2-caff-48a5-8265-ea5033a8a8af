const { DateTime } = require('luxon');

module.exports = function (eleventyConfig) {
  // Add plugins
  eleventyConfig.addPlugin(require('@11ty/eleventy-plugin-rss'));
  eleventyConfig.addPlugin(require('@11ty/eleventy-plugin-syntaxhighlight'));
  eleventyConfig.addPlugin(require('@11ty/eleventy-plugin-bundle'));

  // Copy static assets
  eleventyConfig.addPassthroughCopy('src/js');
  eleventyConfig.addPassthroughCopy('src/assets');
  eleventyConfig.addPassthroughCopy('src/admin');
  eleventyConfig.addPassthroughCopy({ 'src/index.css': 'index.css' });

  // Add collections
  eleventyConfig.addCollection('blog', function (collection) {
    return collection.getFilteredByGlob('src/blog/*.md').sort((a, b) => {
      return b.date - a.date;
    });
  });

  eleventyConfig.addCollection('portfolio', function (collection) {
    return collection.getFilteredByGlob('src/portfolio/*.md').sort((a, b) => {
      return b.date - a.date;
    });
  });

  eleventyConfig.addCollection('services', function (collection) {
    return collection.getFilteredByGlob('src/services/*.md');
  });

  // Add filters
  eleventyConfig.addFilter('readableDate', dateObj => {
    return DateTime.fromJSDate(dateObj, { zone: 'utc' }).toFormat(
      'dd LLL yyyy'
    );
  });

  eleventyConfig.addFilter('htmlDateString', dateObj => {
    return DateTime.fromJSDate(dateObj, { zone: 'utc' }).toFormat('yyyy-LL-dd');
  });

  eleventyConfig.addFilter('excerpt', content => {
    const excerpt = content.replace(/(<([^>]+)>)/gi, '');
    return excerpt.substr(0, 200) + '...';
  });

  // Watch CSS files for changes
  eleventyConfig.addWatchTarget('./src/index.css');
  eleventyConfig.addWatchTarget('./tailwind.config.js');

  // Set custom markdown library
  let markdownIt = require('markdown-it');
  let markdownItAnchor = require('markdown-it-anchor');

  let markdownLibrary = markdownIt({
    html: true,
    breaks: true,
    linkify: true,
  }).use(markdownItAnchor, {
    permalink: markdownItAnchor.permalink.ariaHidden({
      placement: 'after',
      class: 'direct-link',
      symbol: '#',
    }),
    level: [1, 2, 3, 4],
    slugify: eleventyConfig.getFilter('slug'),
  });

  eleventyConfig.setLibrary('md', markdownLibrary);

  return {
    templateFormats: ['md', 'njk', 'html', 'liquid'],
    markdownTemplateEngine: 'njk',
    htmlTemplateEngine: 'njk',
    dir: {
      input: 'src',
      includes: '_includes',
      data: '_data',
      output: '_site',
    },
  };
};
